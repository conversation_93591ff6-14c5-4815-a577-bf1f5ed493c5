import { defHttp } from '@/utils/http/axios';
import {
  LoginParams,
  LoginResultModel,
  GetUserInfoModel,
  refreshTokenPrams,
  GetSysConfigModel,
} from './model/userModel';

import { ErrorMessageMode } from '#/axios';

enum Api {
  Login = '/api/system/login',
  RefreshToken = '/api/system/refresh_token',
  Logout = '/api/system/logout',
  GetUserInfo = '/api/system/userinfo',
  GetSysConfig = '/api/system/sys_config',
  GetPermCode = '/api/system/permCode',
  TestRetry = '/testRetry',
  Register = '/api/system/register',
}

/**
 * @description: user login api
 */
export function loginApi(params: LoginParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<LoginResultModel>(
    {
      url: Api.Login,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

export function refreshTokenApi(params: refreshTokenPrams, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<LoginResultModel>(
    {
      url: Api.RefreshToken,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

export function registerApi(params: refreshTokenPrams) {
  return defHttp.post<LoginResultModel>({
    url: Api.Register,
    params,
  });
}

/**
 * @description: getUserInfo
 */
export function getUserInfo() {
  return defHttp.get<GetUserInfoModel>({ url: Api.GetUserInfo }, { errorMessageMode: 'message' });
}

/**
 * @description: getUserInfo
 */
export function getSysConfig() {
  return defHttp.get<GetSysConfigModel>({ url: Api.GetSysConfig }, { errorMessageMode: 'message' });
}

export function getPermCode() {
  return defHttp.get<string[]>({ url: Api.GetPermCode });
}

export function doLogout() {
  return defHttp.get({ url: Api.Logout });
}

export function testRetry() {
  return defHttp.get(
    { url: Api.TestRetry },
    {
      retryRequest: {
        isOpenRetry: true,
        count: 5,
        waitTime: 1000,
      },
    },
  );
}
