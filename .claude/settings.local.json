{"permissions": {"allow": ["Bash(pnpm add:*)", "Bash(npm config set:*)", "<PERSON><PERSON>(source:*)", "Bash(nvm use:*)", "Bash(npm i:*)", "Read(/Users/<USER>/Documents/yongchuangdaping/**)", "Read(/Users/<USER>/Documents/yongchuangdaping/**)", "Bash(pnpm type:check:*)", "Read(/Users/<USER>/Documents/yongchuangdaping/**)", "Bash(pnpm lint:prettier:*)", "Bash(pnpm dev:*)", "Bash(grep:*)", "<PERSON><PERSON>(sed:*)", "Bash(find:*)"], "deny": [], "additionalDirectories": ["/Users/<USER>/.npm-global"]}}